/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-20 18:18:51
 * @LastEditTime: 2025-08-20 18:33:00
 * @FilePath: /逆向百例/国家医保服务平台/run.js
 */
const CryptoJS = require("crypto-js");
t = {
    headers: {},
};

function i_a() {
    var e,
        t,
        n,
        i = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
        r = "0123456789";
    return (
        (e = o(
            6,
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        )),
        (t = o(1, i)),
        (n = o(1, r)),
        t + n + e
    );
    function o(e, t) {
        e = e || 32;
        for (var n = "", i = 0; i < e; i++)
            n += t.charAt(Math.ceil(1e3 * Math.random()) % t.length);
        return n;
    }
}

function sha256() {
    return CryptoJS.SHA256(t).toString();
}
function signature() {
    (s = Math.ceil(new Date().getTime() / 1e3)), (h = i_a()), (f = s + h + s);
    t.headers["x-tif-signature"] = sha256(f);
    (t.headers["x-tif-timestamp"] = s), (t.headers["x-tif-nonce"] = h);
}

res = signature();
console.log(res);
